#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>
#include <ATen/ops/cat_meta.h>

namespace at {
namespace native {
struct TORCH_API structured_cat_out_cpu : public at::meta::structured_cat {
void impl(const at::ITensorListRef & tensors, int64_t dim, int64_t valid, bool all_contiguous, bool all_same_dtype, bool all_same_sizes_and_stride, at::MemoryFormat memory_format, const at::Tensor & out);
};
struct TORCH_API structured_cat_out_cuda : public at::meta::structured_cat {
void impl(const at::ITensorListRef & tensors, int64_t dim, int64_t valid, bool all_contiguous, bool all_same_dtype, bool all_same_sizes_and_stride, at::MemoryFormat memory_format, const at::Tensor & out);
};
TORCH_API at::Tensor cat_nested(const at::ITensorListRef & tensors, int64_t dim=0);
TORCH_API at::Tensor cat_sparse(const at::ITensorListRef & tensors, int64_t dim=0);
struct TORCH_API structured_cat_out_mps : public at::meta::structured_cat {
void impl(const at::ITensorListRef & tensors, int64_t dim, int64_t valid, bool all_contiguous, bool all_same_dtype, bool all_same_sizes_and_stride, at::MemoryFormat memory_format, const at::Tensor & out);
};
TORCH_API at::Tensor cat_quantized_cpu(const at::ITensorListRef & tensors, int64_t dim=0);
TORCH_API at::Tensor & cat_out_quantized_cpu(const at::ITensorListRef & tensors, int64_t dim, at::Tensor & out);
TORCH_API at::Tensor cat(at::TensorList tensors, at::Dimname dim);
TORCH_API at::Tensor & cat_out(at::TensorList tensors, at::Dimname dim, at::Tensor & out);
} // namespace native
} // namespace at
